import 'package:permission_handler/permission_handler.dart';
import 'package:rooo_driver/features/permissions/screens/location_permission_screen.dart';
import 'package:rooo_driver/global/export/app_export.dart';

class RequiredPermissionScreen extends StatefulWidget {
  @override
  RequiredPermissionScreenState createState() =>
      RequiredPermissionScreenState();
}

class RequiredPermissionScreenState extends State<RequiredPermissionScreen>
    with WidgetsBindingObserver {
  // bool _isNotificationAllowed = false;
  bool _isLocationAllowed = false;
  bool _checking = true;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _checkPermissions();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    setState(() {
      _checking = true;
    });
    if (state == AppLifecycleState.resumed) {
      await _checkPermissions();
      // if (_isNotificationAllowed && _isLocationAllowed) {
      if (_isLocationAllowed) {
        Navigator.of(context).pop(true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(title: "Permissions"),
      body: _checking
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Padding(
              padding: screenPadding,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(child: Icon(Icons.security, size: 100)),
                  const SizedBox(height: 20),
                  Text(
                    "ROOO requires the following permissions to function:",
                  ),
                  height10,
                  // _isNotificationAllowed
                  //     ? Text(
                  //         "• Notifications (Allowed)",
                  //         style: TextStyle(color: Colors.green),
                  //       )
                  //     : Text(
                  //         "• Notifications (Not allowed)",
                  //         style: TextStyle(color: Colors.red),
                  //       ),
                  // height10,
                  _isLocationAllowed
                      ? Text(
                          "• Location (Allowed)",
                          style: TextStyle(color: Colors.green),
                        )
                      : Text(
                          "• Location - even in the background (Not allowed)",
                          style: TextStyle(color: Colors.red),
                        ),
                  height10,
                  Text(
                    "Please allow these permissions to continue.",
                  ),
                  height20,
                  AppButton(
                    width: double.infinity,
                    onPressed: () async {
                      // if (!_isNotificationAllowed) {
                      //   Navigator.push<bool>(
                      //     context,
                      //     MaterialPageRoute(
                      //         builder: (context) =>
                      //             NotificationPermissionScreen()),
                      //   );
                      // } else

                      if (!_isLocationAllowed) {
                        Navigator.push<bool>(
                          context,
                          MaterialPageRoute(
                              builder: (context) => LocationPermissionScreen()),
                        );
                      }
                    },
                    text: "Proceed",
                  ),
                ],
              ),
            ),
    );
  }

  Future<void> _checkPermissions() async {
    await Future.wait([
      Permission.notification.status,
      Permission.locationAlways.status,
    ]).then((values) {
      // _isNotificationAllowed = values[0] == PermissionStatus.granted;
      _isLocationAllowed = values[1] == PermissionStatus.granted;
      setState(() {
        _checking = false;
      });
    });
  }
}
