import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/global/globalMethods/pausable_class.dart';
import 'package:rooo_driver/model/AppSettingModel.dart';
import 'package:location/location.dart' as location;


/// Class to handle different types of admin notification timers
class AdminNotificationTimers {
  // Default values in seconds
  int _normalRideTimer = 300; // 5 minutes
  int _scheduledRideTimer = 300; // 5 minutes
  bool _isScheduledRide = false;

  // Getters
  int get normalRideTimer => _normalRideTimer;
  int get scheduledRideTimer => _scheduledRideTimer;

  // Setters
  set normalRideTimer(int value) {
    if (value > 0) _normalRideTimer = value;
  }

  set scheduledRideTimer(int value) {
    if (value > 0) _scheduledRideTimer = value;
  }

  // Set ride type
  void setRideType({required bool isScheduled}) {
    _isScheduledRide = isScheduled;
  }

  // Get current timer value based on ride type
  int getCurrentTimerValue() {
    return _isScheduledRide ? _scheduledRideTimer : _normalRideTimer;
  }

  // Reset to default values
  void reset() {
    _normalRideTimer = 300;
    _scheduledRideTimer = 300;
    _isScheduledRide = false;
  }
}

class GlobalState {
  static location.LocationData? lastKnownLocation;
  static location.LocationData? latestLocation;
  static Function homePageDataRefresher = () {};
  static bool isDocumentsScreenOpened = false;
  static bool isLiveImageScreenOpened = false;
  static bool isLoggedIn = false;
  static bool isProfileComplete = true;
  static bool isLoggingOut = false;
  static String lastNotificationId = "";
  static ValueNotifier<RideModel>? current_ride;
  static MqttServerClient? mqttClient;
  static LatLng? driverPosition;
  static Timer? driver_device_timer;
  static Timer? driver_server_timer;
  static bool is_foreground_state = true;
  static ValueNotifier<String> location_permission = ValueNotifier("");
  static ValueNotifier<String> location_permission_always = ValueNotifier("");
  static ValueNotifier<String> notification_permission = ValueNotifier("");
  static PausableTimer pausableTimer = PausableTimer(30);
  static int new_ride_countdown = 30;
  static ValueNotifier<int> new_ride_countdown_timer_value = ValueNotifier(0);
  static AdminNotificationTimers adminNotificationTimers =
      AdminNotificationTimers();
  static int get adminNotifyLimit => adminNotificationTimers.getCurrentTimerValue();
  static AudioPlayer audio_new_ride = AudioPlayer();
  static AudioPlayer audio_offline = AudioPlayer();
  static AudioPlayer audio_error = AudioPlayer();
  static AudioPlayer audio_online = AudioPlayer();
  static ValueNotifier<Set<ChatCountModel>> chat_count = ValueNotifier({});
  static ValueNotifier<int> global_care_count = ValueNotifier(0);
  static ValueNotifier<int> global_ride_issues_count = ValueNotifier(0);
  static ValueNotifier<int> global_inbox_count = ValueNotifier(0);
  static ValueNotifier<int> global_notification_count = ValueNotifier(0);
  static ValueNotifier<int> global_opportunity_count = ValueNotifier(0);
  static ValueNotifier<int> global_drawer_count = ValueNotifier(0);
  static List<String> screenList = [];
  static String driverRegionCode = "in";
  static String playerId = "";
  static int? chatRideId;
  static ValueNotifier<String> MqttConnectionState = ValueNotifier("");
  static String? selectedMap = "Apple Maps";
  static AppSettingModel? appSettingModel;
  static bool isInternetErrorOpen = false;
  static void resetState() {
    // darkModeSetting.dispose();
    chat_count.dispose();
    selectedMap = "";

    global_care_count.dispose();
    global_inbox_count.dispose();
    global_notification_count.dispose();
    global_opportunity_count.dispose();
    global_drawer_count.dispose();
    chat_count = ValueNotifier({});
    global_care_count = ValueNotifier(0);
    global_inbox_count = ValueNotifier(0);
    global_notification_count = ValueNotifier(0);

    global_opportunity_count = ValueNotifier(0);

    global_drawer_count = ValueNotifier(0);

    // Dispose of AudioPlayer instances
    audio_new_ride.dispose();
    audio_offline.dispose();
    audio_error.dispose();
    audio_online.dispose();

    // Dispose of ValueNotifier instances
    new_ride_countdown_timer_value.dispose();

    // inprogress_waiting_time_timer_value.dispose();

    // Reset static fields to their initial state
    current_ride = null;
    mqttClient = null;
    driverPosition = null;
    // IS_PROFILE_COMPLETE = false;

    // Reset driverPosition to null

    driver_server_timer?.cancel();
    driver_server_timer = null;
    driver_device_timer?.cancel();
    driver_device_timer = null;

    pausableTimer.cancel();
    pausableTimer =
        PausableTimer(30); // Reinitialize PausableTimer with default value
    new_ride_countdown = 30;

    // Reinitialize ValueNotifier instances
    new_ride_countdown_timer_value =
        ValueNotifier(0); // Reset ValueNotifier to initial value

    // Reset admin notification timers
    adminNotificationTimers.reset();

    // inprogress_waiting_time_timer_value =
    //     ValueNotifier(-1); // Reset ValueNotifier to initial value

    // Reinitialize AudioPlayer instances
    audio_new_ride = AudioPlayer();
    audio_offline = AudioPlayer();
    audio_error = AudioPlayer();
    audio_online = AudioPlayer();
  }
}
